<?php
// ملف لاختبار الـ webhook يدوياً

// بيانات تجريبية تحاكي ما يرسله Evolution API
$testData = [
    "event" => "messages.upsert",
    "instance" => "whatsapp",
    "data" => [
        "key" => [
            "remoteJid" => "<EMAIL>",
            "fromMe" => false,
            "id" => "test_message_" . time()
        ],
        "pushName" => "مستخدم تجريبي",
        "status" => "DELIVERY_ACK",
        "message" => [
            "conversation" => "رسالة تجريبية من الـ webhook - تم إصلاح الكود!"
        ],
        "messageType" => "conversation",
        "messageTimestamp" => time(),
        "instanceId" => "test-instance-id",
        "source" => "android"
    ]
];

echo "<h2>اختبار Webhook</h2>";
echo "<h3>البيانات التي سيتم إرسالها:</h3>";
echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

// إرسال البيانات إلى webhook
$webhookUrl = 'http://localhost/chatbot1/webhook.php';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $webhookUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($testData))
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<h3>استجابة الـ Webhook:</h3>";
echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
echo "<pre>$response</pre>";

// عرض آخر سجلات الـ webhook
echo "<h3>آخر سجلات الـ Webhook:</h3>";
if (file_exists('webhook_log.txt')) {
    $logs = file_get_contents('webhook_log.txt');
    $logLines = explode("\n", $logs);
    $lastLogs = array_slice($logLines, -10); // آخر 10 سجلات
    echo "<pre>" . implode("\n", $lastLogs) . "</pre>";
} else {
    echo "<p>لا يوجد ملف سجل</p>";
}

// عرض آخر الرسائل من قاعدة البيانات
echo "<h3>آخر الرسائل في قاعدة البيانات:</h3>";
try {
    require_once 'database.php';
    $pdo = getDBConnection();
    
    $stmt = $pdo->query("
        SELECT phone, message, type, created_at 
        FROM messages 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($messages) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>الهاتف</th><th>الرسالة</th><th>النوع</th><th>التاريخ</th></tr>";
        foreach ($messages as $msg) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($msg['phone']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['message']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['type']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد رسائل في قاعدة البيانات</p>";
    }
} catch (Exception $e) {
    echo "<p>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    direction: rtl;
}
pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}
table {
    margin-top: 10px;
}
th, td {
    padding: 8px;
    text-align: right;
}
th {
    background: #e9ecef;
}
</style>
