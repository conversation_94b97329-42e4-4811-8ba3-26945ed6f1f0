<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once 'database.php';

try {
    $pdo = getDBConnection();
    
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            // استرجاع الإعدادات
            $stmt = $pdo->prepare("
                SELECT api_url, instance_name, api_key, is_active 
                FROM api_settings 
                WHERE is_active = 1 
                ORDER BY id DESC 
                LIMIT 1
            ");
            $stmt->execute();
            $settings = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($settings) {
                echo json_encode($settings, JSON_UNESCAPED_UNICODE);
            } else {
                echo json_encode([
                    'api_url' => 'http://localhost:8080',
                    'instance_name' => 'default',
                    'api_key' => '',
                    'is_active' => true
                ], JSON_UNESCAPED_UNICODE);
            }
            break;
            
        case 'POST':
        case 'PUT':
            // حفظ أو تحديث الإعدادات
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            if (!$data) {
                throw new Exception('بيانات غير صحيحة');
            }
            
            $apiUrl = isset($data['api_url']) ? trim($data['api_url']) : '';
            $instanceName = isset($data['instance_name']) ? trim($data['instance_name']) : '';
            $apiKey = isset($data['api_key']) ? trim($data['api_key']) : '';
            
            if (empty($apiUrl) || empty($instanceName)) {
                throw new Exception('رابط API واسم الإنستانس مطلوبان');
            }
            
            // تعطيل الإعدادات القديمة
            $pdo->exec("UPDATE api_settings SET is_active = 0");
            
            // إدراج الإعدادات الجديدة
            $stmt = $pdo->prepare("
                INSERT INTO api_settings (api_url, instance_name, api_key, is_active) 
                VALUES (:api_url, :instance_name, :api_key, 1)
            ");
            
            $stmt->bindParam(':api_url', $apiUrl);
            $stmt->bindParam(':instance_name', $instanceName);
            $stmt->bindParam(':api_key', $apiKey);
            
            if ($stmt->execute()) {
                echo json_encode([
                    'success' => true,
                    'message' => 'تم حفظ الإعدادات بنجاح',
                    'data' => [
                        'api_url' => $apiUrl,
                        'instance_name' => $instanceName,
                        'api_key' => $apiKey,
                        'is_active' => true
                    ]
                ], JSON_UNESCAPED_UNICODE);
            } else {
                throw new Exception('فشل في حفظ الإعدادات');
            }
            break;
            
        default:
            throw new Exception('طريقة الطلب غير مدعومة');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
