# WhatsApp Chat System - Evolution API

نظام بسيط لإرسال واستقبال رسائل WhatsApp باستخدام Evolution API مع PHP و MySQL.

## المتطلبات

- XAMPP أو أي خادم ويب يدعم PHP و MySQL
- Evolution API مثبت ومُشغل
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث

## التثبيت

### 1. إعداد قاعدة البيانات

```bash
# افتح المتصفح وانتقل إلى:
http://localhost/chatbot1/database.php
```

هذا سيقوم بإنشاء قاعدة البيانات والجداول المطلوبة تلقائياً.

### 2. إعداد Evolution API

تأكد من أن Evolution API يعمل على:
```
http://localhost:8080
```

### 3. تكوين Webhook (اختياري)

لاستقبال الرسائل تلقائياً، قم بتكوين webhook في Evolution API:

```bash
curl -X POST "http://localhost:8080/webhook/set/default" \
  -H "Content-Type: application/json" \
  -H "apikey: YOUR_API_KEY" \
  -d '{
    "url": "http://localhost/chatbot1/webhook.php",
    "events": ["messages.upsert"]
  }'
```

## الاستخدام

### 1. فتح الواجهة

افتح المتصفح وانتقل إلى:
```
http://localhost/chatbot1/whatsapp_chat.html
```

### 2. تكوين الإعدادات

- **رابط API**: `http://localhost:8080`
- **اسم الإنستانس**: `default` (أو اسم الإنستانس الخاص بك)
- **API Key**: مفتاح API الخاص بك

### 3. اختبار الاتصال

اضغط على زر "اختبار الاتصال" للتأكد من أن الإعدادات صحيحة.

### 4. إرسال رسالة

1. أدخل رقم الهاتف مع رمز البلد (مثال: 966501234567)
2. اكتب الرسالة
3. اضغط "إرسال"

## الملفات

- `whatsapp_chat.html` - الواجهة الرئيسية
- `database.php` - إعداد قاعدة البيانات
- `save_message.php` - حفظ الرسائل
- `get_messages.php` - استرجاع الرسائل
- `webhook.php` - استقبال الرسائل من Evolution API

## قاعدة البيانات

### جدول messages
```sql
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('sent', 'received') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### جدول api_settings
```sql
CREATE TABLE api_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    api_url VARCHAR(255) NOT NULL,
    instance_name VARCHAR(100) NOT NULL,
    api_key VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API Endpoints

### حفظ رسالة
```
POST /save_message.php
Content-Type: application/json

{
    "phone": "966501234567",
    "message": "مرحبا",
    "type": "sent"
}
```

### استرجاع الرسائل
```
GET /get_messages.php?limit=50&offset=0&phone=966501234567
```

### Webhook لاستقبال الرسائل
```
POST /webhook.php
```

## المميزات

- ✅ إرسال رسائل WhatsApp
- ✅ استقبال الرسائل تلقائياً (عبر webhook)
- ✅ حفظ الرسائل في قاعدة البيانات
- ✅ واجهة عربية بسيطة
- ✅ تحديث الرسائل تلقائياً
- ✅ دعم الرسائل النصية والوسائط
- ✅ تسجيل الأخطاء

## استكشاف الأخطاء

### 1. خطأ في الاتصال بـ Evolution API
- تأكد من أن Evolution API يعمل
- تحقق من رابط API
- تأكد من صحة API Key

### 2. خطأ في قاعدة البيانات
- تأكد من تشغيل MySQL
- تحقق من إعدادات قاعدة البيانات في `database.php`

### 3. عدم استقبال الرسائل
- تأكد من تكوين webhook بشكل صحيح
- تحقق من ملف `webhook_log.txt` للأخطاء

## الأمان

⚠️ **تحذير**: هذا النظام مخصص للاستخدام المحلي أو التطوير. للاستخدام في الإنتاج:

- استخدم HTTPS
- أضف مصادقة للمستخدمين
- قم بتشفير API Keys
- أضف حماية من CSRF
- استخدم prepared statements (موجود بالفعل)

## الدعم

للمساعدة أو الإبلاغ عن مشاكل، يرجى التحقق من:
- ملف `webhook_log.txt` للأخطاء
- سجلات خادم الويب
- سجلات Evolution API
