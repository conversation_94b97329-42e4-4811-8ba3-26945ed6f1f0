<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once 'database.php';

try {
    // الحصول على اتصال قاعدة البيانات
    $pdo = getDBConnection();
    
    // معاملات الاستعلام
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
    $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
    $phone = isset($_GET['phone']) ? trim($_GET['phone']) : '';
    
    // التحقق من صحة المعاملات
    if ($limit > 100) $limit = 100; // حد أقصى 100 رسالة
    if ($limit < 1) $limit = 50;
    if ($offset < 0) $offset = 0;
    
    // بناء الاستعلام
    $whereClause = '';
    $params = [];
    
    if (!empty($phone)) {
        $whereClause = 'WHERE phone = :phone';
        $params[':phone'] = $phone;
    }
    
    // استعلام الرسائل
    $sql = "
        SELECT id, phone, message, type, created_at 
        FROM messages 
        $whereClause 
        ORDER BY created_at ASC 
        LIMIT :limit OFFSET :offset
    ";
    
    $stmt = $pdo->prepare($sql);
    
    // ربط المعاملات
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $stmt->execute();
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // استعلام العدد الإجمالي
    $countSql = "SELECT COUNT(*) as total FROM messages $whereClause";
    $countStmt = $pdo->prepare($countSql);
    
    foreach ($params as $key => $value) {
        $countStmt->bindValue($key, $value);
    }
    
    $countStmt->execute();
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // تنسيق البيانات
    $formattedMessages = [];
    foreach ($messages as $msg) {
        $formattedMessages[] = [
            'id' => (int)$msg['id'],
            'phone' => $msg['phone'],
            'message' => $msg['message'],
            'type' => $msg['type'],
            'created_at' => $msg['created_at'],
            'formatted_time' => date('H:i', strtotime($msg['created_at'])),
            'formatted_date' => date('Y-m-d', strtotime($msg['created_at']))
        ];
    }
    
    // إرجاع النتائج
    echo json_encode($formattedMessages, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
