<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once 'database.php';

try {
    $pdo = getDBConnection();
    
    // إحصائيات عامة
    $generalStats = [];
    
    // إجمالي الرسائل
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM messages");
    $generalStats['total_messages'] = (int)$stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // الرسائل المرسلة
    $stmt = $pdo->query("SELECT COUNT(*) as sent FROM messages WHERE type = 'sent'");
    $generalStats['sent_messages'] = (int)$stmt->fetch(PDO::FETCH_ASSOC)['sent'];
    
    // الرسائل المستقبلة
    $stmt = $pdo->query("SELECT COUNT(*) as received FROM messages WHERE type = 'received'");
    $generalStats['received_messages'] = (int)$stmt->fetch(PDO::FETCH_ASSOC)['received'];
    
    // عدد جهات الاتصال الفريدة
    $stmt = $pdo->query("SELECT COUNT(DISTINCT phone) as unique_contacts FROM messages");
    $generalStats['unique_contacts'] = (int)$stmt->fetch(PDO::FETCH_ASSOC)['unique_contacts'];
    
    // رسائل اليوم
    $stmt = $pdo->query("SELECT COUNT(*) as today FROM messages WHERE DATE(created_at) = CURDATE()");
    $generalStats['today_messages'] = (int)$stmt->fetch(PDO::FETCH_ASSOC)['today'];
    
    // رسائل هذا الأسبوع
    $stmt = $pdo->query("SELECT COUNT(*) as week FROM messages WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $generalStats['week_messages'] = (int)$stmt->fetch(PDO::FETCH_ASSOC)['week'];
    
    // أكثر جهات الاتصال نشاطاً
    $stmt = $pdo->query("
        SELECT phone, COUNT(*) as message_count 
        FROM messages 
        GROUP BY phone 
        ORDER BY message_count DESC 
        LIMIT 10
    ");
    $topContacts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات يومية للأسبوع الماضي
    $stmt = $pdo->query("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as total,
            SUM(CASE WHEN type = 'sent' THEN 1 ELSE 0 END) as sent,
            SUM(CASE WHEN type = 'received' THEN 1 ELSE 0 END) as received
        FROM messages 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
    ");
    $dailyStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات ساعية لليوم الحالي
    $stmt = $pdo->query("
        SELECT 
            HOUR(created_at) as hour,
            COUNT(*) as total,
            SUM(CASE WHEN type = 'sent' THEN 1 ELSE 0 END) as sent,
            SUM(CASE WHEN type = 'received' THEN 1 ELSE 0 END) as received
        FROM messages 
        WHERE DATE(created_at) = CURDATE()
        GROUP BY HOUR(created_at)
        ORDER BY hour
    ");
    $hourlyStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // آخر الرسائل
    $stmt = $pdo->query("
        SELECT phone, message, type, created_at 
        FROM messages 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $recentMessages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تنسيق البيانات
    foreach ($recentMessages as &$msg) {
        $msg['formatted_time'] = date('H:i', strtotime($msg['created_at']));
        $msg['formatted_date'] = date('Y-m-d', strtotime($msg['created_at']));
        // قطع الرسالة إذا كانت طويلة
        if (strlen($msg['message']) > 50) {
            $msg['message'] = substr($msg['message'], 0, 50) . '...';
        }
    }
    
    foreach ($topContacts as &$contact) {
        $contact['message_count'] = (int)$contact['message_count'];
    }
    
    foreach ($dailyStats as &$day) {
        $day['total'] = (int)$day['total'];
        $day['sent'] = (int)$day['sent'];
        $day['received'] = (int)$day['received'];
        $day['formatted_date'] = date('d/m', strtotime($day['date']));
    }
    
    foreach ($hourlyStats as &$hour) {
        $hour['hour'] = (int)$hour['hour'];
        $hour['total'] = (int)$hour['total'];
        $hour['sent'] = (int)$hour['sent'];
        $hour['received'] = (int)$hour['received'];
    }
    
    // إرجاع جميع الإحصائيات
    echo json_encode([
        'general' => $generalStats,
        'top_contacts' => $topContacts,
        'daily_stats' => $dailyStats,
        'hourly_stats' => $hourlyStats,
        'recent_messages' => $recentMessages,
        'generated_at' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
