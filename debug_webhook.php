<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة الـ Webhook</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { padding: 8px; text-align: right; border: 1px solid #ddd; }
        th { background: #e9ecef; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشكلة الـ Webhook</h1>
        
        <?php
        require_once 'database.php';
        
        // 1. فحص قاعدة البيانات
        echo "<div class='section info'>";
        echo "<h3>1. فحص قاعدة البيانات</h3>";
        try {
            $pdo = getDBConnection();
            echo "<p>✅ الاتصال بقاعدة البيانات ناجح</p>";
            
            // عدد الرسائل
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM messages");
            $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            echo "<p>📊 إجمالي الرسائل: $total</p>";
            
            // الرسائل المستقبلة
            $stmt = $pdo->query("SELECT COUNT(*) as received FROM messages WHERE type = 'received'");
            $received = $stmt->fetch(PDO::FETCH_ASSOC)['received'];
            echo "<p>📥 الرسائل المستقبلة: $received</p>";
            
            // الرسائل المرسلة
            $stmt = $pdo->query("SELECT COUNT(*) as sent FROM messages WHERE type = 'sent'");
            $sent = $stmt->fetch(PDO::FETCH_ASSOC)['sent'];
            echo "<p>📤 الرسائل المرسلة: $sent</p>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
        }
        echo "</div>";
        
        // 2. فحص ملف السجل
        echo "<div class='section info'>";
        echo "<h3>2. فحص ملف السجل</h3>";
        if (file_exists('webhook_log.txt')) {
            $logSize = filesize('webhook_log.txt');
            echo "<p>📄 حجم ملف السجل: " . number_format($logSize) . " بايت</p>";
            
            $logs = file_get_contents('webhook_log.txt');
            $logLines = explode("\n", trim($logs));
            echo "<p>📝 عدد السجلات: " . count($logLines) . "</p>";
            
            // آخر 3 سجلات
            $recentLogs = array_slice($logLines, -3);
            echo "<h4>آخر 3 سجلات:</h4>";
            foreach ($recentLogs as $log) {
                if (!empty(trim($log))) {
                    echo "<pre>" . htmlspecialchars($log) . "</pre>";
                }
            }
        } else {
            echo "<p>❌ ملف السجل غير موجود</p>";
        }
        echo "</div>";
        
        // 3. تحليل رسائل messages.upsert
        echo "<div class='section info'>";
        echo "<h3>3. تحليل رسائل messages.upsert</h3>";
        if (file_exists('webhook_log.txt')) {
            $logs = file_get_contents('webhook_log.txt');
            $messageUpserts = [];
            
            $logLines = explode("\n", $logs);
            foreach ($logLines as $line) {
                if (strpos($line, 'messages.upsert') !== false) {
                    $messageUpserts[] = $line;
                }
            }
            
            echo "<p>📨 عدد أحداث messages.upsert: " . count($messageUpserts) . "</p>";
            
            if (count($messageUpserts) > 0) {
                echo "<h4>آخر حدث messages.upsert:</h4>";
                $lastUpsert = end($messageUpserts);
                
                // استخراج JSON من السجل
                if (preg_match('/\[(.*?)\] (.*)$/', $lastUpsert, $matches)) {
                    $jsonData = json_decode($matches[2], true);
                    if ($jsonData && isset($jsonData['body'])) {
                        echo "<pre>" . json_encode($jsonData['body'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                        
                        // تحليل البيانات
                        $eventData = $jsonData['body'];
                        if (isset($eventData['data'])) {
                            echo "<h4>تحليل البيانات:</h4>";
                            echo "<ul>";
                            echo "<li><strong>Event:</strong> " . ($eventData['event'] ?? 'غير محدد') . "</li>";
                            echo "<li><strong>Instance:</strong> " . ($eventData['instance'] ?? 'غير محدد') . "</li>";
                            
                            $data = $eventData['data'];
                            if (isset($data['key'])) {
                                echo "<li><strong>Remote JID:</strong> " . ($data['key']['remoteJid'] ?? 'غير محدد') . "</li>";
                                echo "<li><strong>From Me:</strong> " . (isset($data['key']['fromMe']) ? ($data['key']['fromMe'] ? 'true' : 'false') : 'غير محدد') . "</li>";
                            }
                            
                            if (isset($data['message'])) {
                                echo "<li><strong>Message Type:</strong> " . implode(', ', array_keys($data['message'])) . "</li>";
                                if (isset($data['message']['conversation'])) {
                                    echo "<li><strong>Message Text:</strong> " . htmlspecialchars($data['message']['conversation']) . "</li>";
                                }
                            }
                            echo "</ul>";
                        }
                    }
                }
            }
        }
        echo "</div>";
        
        // 4. آخر الرسائل في قاعدة البيانات
        echo "<div class='section info'>";
        echo "<h3>4. آخر الرسائل في قاعدة البيانات</h3>";
        try {
            $stmt = $pdo->query("
                SELECT phone, message, type, created_at 
                FROM messages 
                ORDER BY created_at DESC 
                LIMIT 5
            ");
            $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($messages) {
                echo "<table>";
                echo "<tr><th>الهاتف</th><th>الرسالة</th><th>النوع</th><th>التاريخ</th></tr>";
                foreach ($messages as $msg) {
                    $typeColor = $msg['type'] === 'sent' ? 'blue' : 'green';
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($msg['phone']) . "</td>";
                    echo "<td>" . htmlspecialchars(substr($msg['message'], 0, 50)) . (strlen($msg['message']) > 50 ? '...' : '') . "</td>";
                    echo "<td style='color: $typeColor;'>" . htmlspecialchars($msg['type']) . "</td>";
                    echo "<td>" . htmlspecialchars($msg['created_at']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>❌ لا توجد رسائل في قاعدة البيانات</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ خطأ في قراءة الرسائل: " . $e->getMessage() . "</p>";
        }
        echo "</div>";
        
        // 5. أدوات التشخيص
        echo "<div class='section'>";
        echo "<h3>5. أدوات التشخيص</h3>";
        echo "<button onclick=\"window.open('test_webhook.php', '_blank')\">اختبار الـ Webhook</button>";
        echo "<button onclick=\"window.open('send_test_message.php', '_blank')\">إرسال رسالة تجريبية</button>";
        echo "<button onclick=\"window.open('view_logs.php', '_blank')\">عرض السجل المفصل</button>";
        echo "<button onclick=\"window.open('whatsapp_chat.html', '_blank')\">فتح الشات</button>";
        echo "<button onclick=\"location.reload()\">تحديث الصفحة</button>";
        echo "</div>";
        ?>
    </div>
</body>
</html>
