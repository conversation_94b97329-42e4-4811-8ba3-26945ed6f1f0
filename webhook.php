<?php
header('Content-Type: application/json; charset=utf-8');

require_once 'database.php';

// تسجيل جميع الطلبات الواردة للتشخيص
function logRequest($data) {
    $logFile = 'webhook_log.txt';
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

try {
    // قراءة البيانات الواردة
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    // تسجيل الطلب
    logRequest([
        'method' => $_SERVER['REQUEST_METHOD'],
        'headers' => getallheaders(),
        'body' => $data
    ]);
    
    // التحقق من وجود البيانات
    if (!$data) {
        throw new Exception('لا توجد بيانات');
    }
    
    // التحقق من نوع الحدث
    if (!isset($data['event']) || $data['event'] !== 'messages.upsert') {
        // إرجاع استجابة ناجحة للأحداث الأخرى
        echo json_encode(['success' => true, 'message' => 'تم استلام الحدث']);
        exit;
    }

    // التحقق من وجود البيانات المطلوبة
    if (!isset($data['data'])) {
        throw new Exception('بيانات الرسالة غير صحيحة');
    }

    // تحويل البيانات إلى مصفوفة إذا لم تكن كذلك
    $messagesData = is_array($data['data']) ? $data['data'] : [$data['data']];
    
    $pdo = getDBConnection();
    
    // معالجة كل رسالة
    foreach ($messagesData as $messageData) {
        // تسجيل تفاصيل الرسالة للتشخيص
        logRequest([
            'processing_message' => true,
            'fromMe' => isset($messageData['key']['fromMe']) ? $messageData['key']['fromMe'] : 'not_set',
            'messageKey' => isset($messageData['key']) ? $messageData['key'] : 'no_key',
            'messageContent' => isset($messageData['message']) ? array_keys($messageData['message']) : 'no_message'
        ]);

        // التحقق من أن الرسالة واردة وليست صادرة
        if (isset($messageData['key']['fromMe']) && $messageData['key']['fromMe'] === true) {
            logRequest(['skipped_message' => 'fromMe is true']);
            continue; // تجاهل الرسائل الصادرة منا
        }
        
        // استخراج معلومات الرسالة
        $phone = '';
        $message = '';
        
        // الحصول على رقم الهاتف
        if (isset($messageData['key']['remoteJid'])) {
            $phone = str_replace('@s.whatsapp.net', '', $messageData['key']['remoteJid']);
        }
        
        // الحصول على نص الرسالة
        if (isset($messageData['message'])) {
            $msgContent = $messageData['message'];
            
            if (isset($msgContent['conversation'])) {
                $message = $msgContent['conversation'];
            } elseif (isset($msgContent['extendedTextMessage']['text'])) {
                $message = $msgContent['extendedTextMessage']['text'];
            } elseif (isset($msgContent['imageMessage']['caption'])) {
                $message = '[صورة] ' . $msgContent['imageMessage']['caption'];
            } elseif (isset($msgContent['videoMessage']['caption'])) {
                $message = '[فيديو] ' . $msgContent['videoMessage']['caption'];
            } elseif (isset($msgContent['documentMessage']['caption'])) {
                $message = '[مستند] ' . $msgContent['documentMessage']['caption'];
            } elseif (isset($msgContent['audioMessage'])) {
                $message = '[رسالة صوتية]';
            } elseif (isset($msgContent['imageMessage'])) {
                $message = '[صورة]';
            } elseif (isset($msgContent['videoMessage'])) {
                $message = '[فيديو]';
            } elseif (isset($msgContent['documentMessage'])) {
                $message = '[مستند]';
            } else {
                $message = '[نوع رسالة غير مدعوم]';
            }
        }
        
        // تسجيل البيانات المستخرجة
        logRequest([
            'extracted_data' => true,
            'phone' => $phone,
            'message' => $message,
            'phone_empty' => empty($phone),
            'message_empty' => empty($message)
        ]);

        // التحقق من وجود البيانات الأساسية
        if (empty($phone) || empty($message)) {
            logRequest(['skipped_message' => 'empty phone or message', 'phone' => $phone, 'message' => $message]);
            continue;
        }
        
        // التحقق من عدم وجود الرسالة مسبقاً (تجنب التكرار)
        $messageId = isset($messageData['key']['id']) ? $messageData['key']['id'] : '';
        
        if (!empty($messageId)) {
            $checkStmt = $pdo->prepare("
                SELECT COUNT(*) FROM messages 
                WHERE phone = :phone AND message = :message 
                AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)
            ");
            $checkStmt->bindParam(':phone', $phone);
            $checkStmt->bindParam(':message', $message);
            $checkStmt->execute();
            
            if ($checkStmt->fetchColumn() > 0) {
                continue; // الرسالة موجودة مسبقاً
            }
        }
        
        // حفظ الرسالة في قاعدة البيانات
        $stmt = $pdo->prepare("
            INSERT INTO messages (phone, message, type) 
            VALUES (:phone, :message, 'received')
        ");
        
        $stmt->bindParam(':phone', $phone);
        $stmt->bindParam(':message', $message);
        
        if ($stmt->execute()) {
            // تسجيل نجاح حفظ الرسالة
            logRequest([
                'action' => 'message_saved',
                'phone' => $phone,
                'message' => $message,
                'id' => $pdo->lastInsertId()
            ]);
        }
    }
    
    // إرجاع استجابة ناجحة
    echo json_encode([
        'success' => true,
        'message' => 'تم معالجة الرسائل بنجاح'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // تسجيل الخطأ
    logRequest([
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    // تسجيل خطأ قاعدة البيانات
    logRequest([
        'db_error' => $e->getMessage()
    ]);
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ], JSON_UNESCAPED_UNICODE);
}
?>
