<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (file_exists('webhook_log.txt')) {
        file_put_contents('webhook_log.txt', '');
        echo json_encode(['success' => true, 'message' => 'تم مسح السجل']);
    } else {
        echo json_encode(['success' => false, 'message' => 'ملف السجل غير موجود']);
    }
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
}
?>
