<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once 'database.php';

try {
    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير مدعومة');
    }
    
    // قراءة البيانات من JSON
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('بيانات غير صحيحة');
    }
    
    // التحقق من وجود البيانات المطلوبة
    if (empty($data['phone']) || empty($data['message']) || empty($data['type'])) {
        throw new Exception('بيانات مفقودة: يجب توفير phone, message, type');
    }
    
    $phone = trim($data['phone']);
    $message = trim($data['message']);
    $type = trim($data['type']);
    
    // التحقق من صحة نوع الرسالة
    if (!in_array($type, ['sent', 'received'])) {
        throw new Exception('نوع الرسالة غير صحيح');
    }
    
    // الحصول على اتصال قاعدة البيانات
    $pdo = getDBConnection();
    
    // إدراج الرسالة في قاعدة البيانات
    $stmt = $pdo->prepare("
        INSERT INTO messages (phone, message, type) 
        VALUES (:phone, :message, :type)
    ");
    
    $stmt->bindParam(':phone', $phone);
    $stmt->bindParam(':message', $message);
    $stmt->bindParam(':type', $type);
    
    if ($stmt->execute()) {
        $messageId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ الرسالة بنجاح',
            'message_id' => $messageId,
            'data' => [
                'id' => $messageId,
                'phone' => $phone,
                'message' => $message,
                'type' => $type,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ], JSON_UNESCAPED_UNICODE);
    } else {
        throw new Exception('فشل في حفظ الرسالة');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
