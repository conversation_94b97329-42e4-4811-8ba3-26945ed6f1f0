<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل الـ Webhook</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .log-entry {
            background: #f8f9fa;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .log-entry.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .log-entry.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .timestamp {
            font-weight: bold;
            color: #666;
            font-size: 0.9em;
        }
        .log-data {
            margin-top: 10px;
            font-family: monospace;
            background: white;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        .controls {
            margin-bottom: 20px;
            text-align: center;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .clear-btn {
            background: #dc3545;
        }
        .clear-btn:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 سجل الـ Webhook</h1>
        
        <div class="controls">
            <button onclick="location.reload()">تحديث</button>
            <button onclick="clearLogs()" class="clear-btn">مسح السجل</button>
            <button onclick="testWebhook()">اختبار الـ Webhook</button>
            <button onclick="window.open('whatsapp_chat.html', '_blank')">فتح الشات</button>
        </div>
        
        <div id="logsContainer">
            <?php
            if (file_exists('webhook_log.txt')) {
                $logs = file_get_contents('webhook_log.txt');
                if (!empty($logs)) {
                    $logLines = explode("\n", trim($logs));
                    $logLines = array_reverse($logLines); // عرض الأحدث أولاً
                    
                    foreach ($logLines as $line) {
                        if (empty(trim($line))) continue;
                        
                        // استخراج التاريخ والبيانات
                        if (preg_match('/^\[(.*?)\] (.*)$/', $line, $matches)) {
                            $timestamp = $matches[1];
                            $data = $matches[2];
                            
                            // تحديد نوع السجل
                            $class = '';
                            if (strpos($data, 'error') !== false || strpos($data, 'Error') !== false) {
                                $class = 'error';
                            } elseif (strpos($data, 'message_saved') !== false) {
                                $class = 'success';
                            }
                            
                            echo "<div class='log-entry $class'>";
                            echo "<div class='timestamp'>$timestamp</div>";
                            
                            // محاولة تنسيق JSON
                            $jsonData = json_decode($data, true);
                            if ($jsonData) {
                                echo "<div class='log-data'>" . 
                                     htmlspecialchars(json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . 
                                     "</div>";
                            } else {
                                echo "<div class='log-data'>" . htmlspecialchars($data) . "</div>";
                            }
                            echo "</div>";
                        }
                    }
                } else {
                    echo "<p>ملف السجل فارغ</p>";
                }
            } else {
                echo "<p>لا يوجد ملف سجل</p>";
            }
            ?>
        </div>
    </div>

    <script>
        function clearLogs() {
            if (confirm('هل أنت متأكد من مسح السجل؟')) {
                fetch('clear_logs.php', {method: 'POST'})
                .then(() => location.reload())
                .catch(err => alert('خطأ في مسح السجل'));
            }
        }
        
        function testWebhook() {
            window.open('test_webhook.php', '_blank');
        }
        
        // تحديث تلقائي كل 10 ثوان
        setInterval(() => {
            location.reload();
        }, 10000);
    </script>
</body>
</html>
