<?php
// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'whatsapp_chat';

// الاتصال بـ MySQL
try {
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء قاعدة البيانات إذا لم تكن موجودة
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء جدول الرسائل
    $createTable = "
    CREATE TABLE IF NOT EXISTS messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        phone VARCHAR(20) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('sent', 'received') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_phone (phone),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTable);
    
    // إنشاء جدول إعدادات API
    $createSettingsTable = "
    CREATE TABLE IF NOT EXISTS api_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        api_url VARCHAR(255) NOT NULL,
        instance_name VARCHAR(100) NOT NULL,
        api_key VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createSettingsTable);
    
    // إدراج إعدادات افتراضية إذا لم تكن موجودة
    $checkSettings = $pdo->query("SELECT COUNT(*) FROM api_settings")->fetchColumn();
    if ($checkSettings == 0) {
        $insertDefaultSettings = "
        INSERT INTO api_settings (api_url, instance_name, api_key) 
        VALUES ('http://localhost:8080', 'default', '')
        ";
        $pdo->exec($insertDefaultSettings);
    }
    
    echo "تم إنشاء قاعدة البيانات والجداول بنجاح!";
    
} catch(PDOException $e) {
    die("خطأ في قاعدة البيانات: " . $e->getMessage());
}

// دالة للحصول على اتصال قاعدة البيانات
function getDBConnection() {
    global $host, $username, $password, $database;
    
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch(PDOException $e) {
        die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
    }
}
?>
