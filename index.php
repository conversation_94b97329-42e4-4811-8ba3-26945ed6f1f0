<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام WhatsApp - Evolution API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .btn {
            display: inline-block;
            background: #25D366;
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 8px;
            transition: background 0.3s;
            font-weight: bold;
        }
        
        .btn:hover {
            background: #128C7E;
        }
        
        .btn.secondary {
            background: #007bff;
        }
        
        .btn.secondary:hover {
            background: #0056b3;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #25D366;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-online {
            background: #28a745;
        }
        
        .status-offline {
            background: #dc3545;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💬 نظام WhatsApp</h1>
            <p>إرسال واستقبال رسائل WhatsApp باستخدام Evolution API</p>
        </div>
        
        <div class="cards">
            <div class="card">
                <h3>📱 إرسال واستقبال الرسائل</h3>
                <p>واجهة بسيطة لإرسال واستقبال رسائل WhatsApp مع حفظ تلقائي في قاعدة البيانات.</p>
                <a href="whatsapp_chat.html" class="btn">فتح الشات</a>
            </div>
            
            <div class="card">
                <h3>📊 الإحصائيات</h3>
                <p>عرض إحصائيات مفصلة عن الرسائل المرسلة والمستقبلة وأكثر جهات الاتصال نشاطاً.</p>
                <a href="#" onclick="loadStats()" class="btn secondary">عرض الإحصائيات</a>
                
                <div id="statsContainer" style="display: none;">
                    <div class="stats-grid" id="statsGrid">
                        <!-- سيتم تحميل الإحصائيات هنا -->
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>⚙️ إعداد النظام</h3>
                <p>تكوين إعدادات Evolution API وإنشاء قاعدة البيانات.</p>
                <a href="database.php" class="btn secondary" target="_blank">إعداد قاعدة البيانات</a>
                
                <div style="margin-top: 15px;">
                    <strong>حالة النظام:</strong>
                    <span id="systemStatus">جاري التحقق...</span>
                    <span class="status-indicator" id="statusIndicator"></span>
                </div>
            </div>
            
            <div class="card">
                <h3>📋 سجل الأحداث</h3>
                <p>مراقبة الرسائل الواردة والأخطاء من خلال ملف السجل.</p>
                <a href="#" onclick="viewLogs()" class="btn secondary">عرض السجل</a>
                
                <div id="logsContainer" style="display: none; margin-top: 15px;">
                    <textarea id="logsContent" style="width: 100%; height: 200px; font-family: monospace; font-size: 12px;" readonly></textarea>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>نظام WhatsApp Chat - Evolution API | تم التطوير باستخدام PHP و MySQL</p>
        </div>
    </div>

    <script>
        // تحميل الإحصائيات
        async function loadStats() {
            const container = document.getElementById('statsContainer');
            const grid = document.getElementById('statsGrid');
            
            try {
                const response = await fetch('stats.php');
                if (response.ok) {
                    const data = await response.json();
                    
                    grid.innerHTML = `
                        <div class="stat-item">
                            <div class="stat-number">${data.general.total_messages}</div>
                            <div class="stat-label">إجمالي الرسائل</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${data.general.sent_messages}</div>
                            <div class="stat-label">رسائل مرسلة</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${data.general.received_messages}</div>
                            <div class="stat-label">رسائل مستقبلة</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${data.general.unique_contacts}</div>
                            <div class="stat-label">جهات اتصال</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${data.general.today_messages}</div>
                            <div class="stat-label">رسائل اليوم</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${data.general.week_messages}</div>
                            <div class="stat-label">رسائل الأسبوع</div>
                        </div>
                    `;
                    
                    container.style.display = 'block';
                } else {
                    alert('فشل في تحميل الإحصائيات');
                }
            } catch (error) {
                alert('خطأ في تحميل الإحصائيات: ' + error.message);
            }
        }
        
        // عرض السجل
        async function viewLogs() {
            const container = document.getElementById('logsContainer');
            const content = document.getElementById('logsContent');
            
            try {
                const response = await fetch('webhook_log.txt');
                if (response.ok) {
                    const logs = await response.text();
                    content.value = logs || 'لا توجد سجلات متاحة';
                    container.style.display = 'block';
                } else {
                    content.value = 'لا يمكن الوصول إلى ملف السجل';
                    container.style.display = 'block';
                }
            } catch (error) {
                content.value = 'خطأ في تحميل السجل: ' + error.message;
                container.style.display = 'block';
            }
        }
        
        // فحص حالة النظام
        async function checkSystemStatus() {
            const statusElement = document.getElementById('systemStatus');
            const indicatorElement = document.getElementById('statusIndicator');
            
            try {
                // فحص قاعدة البيانات
                const dbResponse = await fetch('get_messages.php?limit=1');
                
                if (dbResponse.ok) {
                    statusElement.textContent = 'النظام يعمل بشكل طبيعي';
                    indicatorElement.className = 'status-indicator status-online';
                } else {
                    statusElement.textContent = 'مشكلة في قاعدة البيانات';
                    indicatorElement.className = 'status-indicator status-offline';
                }
            } catch (error) {
                statusElement.textContent = 'خطأ في الاتصال';
                indicatorElement.className = 'status-indicator status-offline';
            }
        }
        
        // تشغيل فحص الحالة عند تحميل الصفحة
        window.onload = function() {
            checkSystemStatus();
        };
    </script>
</body>
</html>
