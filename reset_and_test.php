<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين واختبار النظام</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .danger { background: #dc3545; }
        .danger:hover { background: #c82333; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 إعادة تعيين واختبار النظام</h1>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (isset($_POST['clear_log'])) {
                // مسح السجل
                if (file_exists('webhook_log.txt')) {
                    file_put_contents('webhook_log.txt', '');
                    echo "<div class='section success'>✅ تم مسح سجل الـ Webhook</div>";
                } else {
                    echo "<div class='section info'>ℹ️ ملف السجل غير موجود</div>";
                }
            }
            
            if (isset($_POST['test_webhook'])) {
                // اختبار الـ webhook
                $testData = [
                    "event" => "messages.upsert",
                    "instance" => "whatsapp",
                    "data" => [
                        "key" => [
                            "remoteJid" => "<EMAIL>",
                            "fromMe" => false,
                            "id" => "RESET_TEST_" . time()
                        ],
                        "pushName" => "الشبل",
                        "status" => "DELIVERY_ACK",
                        "message" => [
                            "conversation" => "رسالة اختبار بعد إصلاح الكود - " . date('H:i:s')
                        ],
                        "messageType" => "conversation",
                        "messageTimestamp" => time(),
                        "instanceId" => "98855d3a-6a9a-4835-9f50-fc43641a904f",
                        "source" => "android"
                    ]
                ];
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'http://localhost/chatbot1/webhook.php');
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json'
                ]);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                echo "<div class='section info'>";
                echo "<h3>نتيجة اختبار الـ Webhook:</h3>";
                echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
                echo "<p><strong>الاستجابة:</strong></p>";
                echo "<pre>" . htmlspecialchars($response) . "</pre>";
                echo "</div>";
            }
        }
        ?>
        
        <div class="section">
            <h3>🧹 مسح السجل</h3>
            <p>مسح سجل الـ Webhook لبدء اختبار جديد</p>
            <form method="post" style="display: inline;">
                <button type="submit" name="clear_log" class="danger">مسح السجل</button>
            </form>
        </div>
        
        <div class="section">
            <h3>🧪 اختبار الـ Webhook</h3>
            <p>إرسال رسالة اختبار للـ webhook المحدث</p>
            <form method="post" style="display: inline;">
                <button type="submit" name="test_webhook">اختبار الـ Webhook</button>
            </form>
        </div>
        
        <div class="section">
            <h3>📊 فحص النتائج</h3>
            <button onclick="window.open('check_db.php', '_blank')">فحص قاعدة البيانات</button>
            <button onclick="window.open('view_logs.php', '_blank')">عرض السجل</button>
            <button onclick="window.open('whatsapp_chat.html', '_blank')">فتح الشات</button>
        </div>
        
        <div class="section info">
            <h3>📋 خطوات الاختبار:</h3>
            <ol>
                <li><strong>امسح السجل</strong> لبدء اختبار نظيف</li>
                <li><strong>اختبر الـ Webhook</strong> للتأكد من عمل الكود</li>
                <li><strong>أرسل رسالة من WhatsApp</strong> إلى الرقم المتصل</li>
                <li><strong>افحص قاعدة البيانات</strong> للتأكد من حفظ الرسالة</li>
                <li><strong>افتح الشات</strong> للتأكد من ظهور الرسالة</li>
            </ol>
        </div>
        
        <?php
        // عرض حالة النظام الحالية
        echo "<div class='section info'>";
        echo "<h3>📈 حالة النظام الحالية:</h3>";
        
        try {
            require_once 'database.php';
            $pdo = getDBConnection();
            
            $stmt = $pdo->query('SELECT COUNT(*) as total FROM messages');
            $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            $stmt = $pdo->query('SELECT COUNT(*) as received FROM messages WHERE type = "received"');
            $received = $stmt->fetch(PDO::FETCH_ASSOC)['received'];
            
            $stmt = $pdo->query('SELECT COUNT(*) as sent FROM messages WHERE type = "sent"');
            $sent = $stmt->fetch(PDO::FETCH_ASSOC)['sent'];
            
            echo "<ul>";
            echo "<li><strong>إجمالي الرسائل:</strong> $total</li>";
            echo "<li><strong>الرسائل المستقبلة:</strong> $received</li>";
            echo "<li><strong>الرسائل المرسلة:</strong> $sent</li>";
            echo "</ul>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
        }
        
        // حجم ملف السجل
        if (file_exists('webhook_log.txt')) {
            $logSize = filesize('webhook_log.txt');
            echo "<p><strong>حجم ملف السجل:</strong> " . number_format($logSize) . " بايت</p>";
        } else {
            echo "<p><strong>ملف السجل:</strong> غير موجود</p>";
        }
        
        echo "</div>";
        ?>
    </div>
</body>
</html>
