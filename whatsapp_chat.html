<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Chat - Evolution API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: #25D366;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .chat-container {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            background: #f5f5f5;
            border-bottom: 1px solid #ddd;
        }
        
        .message {
            margin: 10px 0;
            padding: 10px 15px;
            border-radius: 18px;
            max-width: 70%;
            word-wrap: break-word;
        }
        
        .message.sent {
            background: #DCF8C6;
            margin-left: auto;
            text-align: right;
        }
        
        .message.received {
            background: white;
            margin-right: auto;
            text-align: left;
        }
        
        .message-time {
            font-size: 11px;
            color: #666;
            margin-top: 5px;
        }
        
        .input-section {
            padding: 20px;
            background: white;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #25D366;
        }
        
        .send-section {
            display: flex;
            gap: 10px;
            align-items: end;
        }
        
        .send-section textarea {
            flex: 1;
            resize: vertical;
            min-height: 50px;
        }
        
        button {
            background: #25D366;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
            height: 50px;
        }
        
        button:hover {
            background: #128C7E;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .config-section {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        
        .refresh-btn {
            background: #007bff;
            margin-left: 10px;
            padding: 8px 15px;
            font-size: 12px;
            height: auto;
        }
        
        .refresh-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💬 WhatsApp Chat - Evolution API</h1>
            <p>إرسال واستقبال رسائل WhatsApp</p>
        </div>
        
        <div class="input-section">
            <!-- إعدادات الاتصال -->
            <div class="config-section">
                <h3>إعدادات Evolution API</h3>
                <div class="form-group">
                    <label for="api_url">رابط API:</label>
                    <input type="url" id="api_url" value="http://localhost:8080" placeholder="http://localhost:8080">
                </div>
                <div class="form-group">
                    <label for="instance_name">اسم الإنستانس:</label>
                    <input type="text" id="instance_name" value="default" placeholder="default">
                </div>
                <div class="form-group">
                    <label for="api_key">API Key:</label>
                    <input type="text" id="api_key" placeholder="your-api-key">
                </div>
                <button onclick="testConnection()">اختبار الاتصال</button>
                <button class="refresh-btn" onclick="loadMessages()">تحديث الرسائل</button>
            </div>
            
            <!-- منطقة الرسائل -->
            <div class="chat-container" id="chatContainer">
                <div class="message received">
                    <div>مرحباً! يمكنك الآن إرسال واستقبال رسائل WhatsApp</div>
                    <div class="message-time">الآن</div>
                </div>
            </div>
            
            <!-- إرسال رسالة -->
            <div class="form-group">
                <label for="phone_number">رقم الهاتف (مع رمز البلد):</label>
                <input type="tel" id="phone_number" placeholder="966501234567" required>
            </div>
            
            <div class="send-section">
                <textarea id="message_text" placeholder="اكتب رسالتك هنا..." required></textarea>
                <button onclick="sendMessage()">إرسال</button>
            </div>
            
            <div id="status"></div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let apiUrl = '';
        let instanceName = '';
        let apiKey = '';
        
        // تحديث المتغيرات من الحقول
        function updateConfig() {
            apiUrl = document.getElementById('api_url').value;
            instanceName = document.getElementById('instance_name').value;
            apiKey = document.getElementById('api_key').value;
        }
        
        // عرض حالة
        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 5000);
        }
        
        // اختبار الاتصال
        async function testConnection() {
            updateConfig();
            
            if (!apiUrl || !instanceName) {
                showStatus('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${apiUrl}/instance/connectionState/${instanceName}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': apiKey
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showStatus(`حالة الاتصال: ${data.instance?.state || 'متصل'}`, 'success');
                } else {
                    showStatus('فشل في الاتصال بـ Evolution API', 'error');
                }
            } catch (error) {
                showStatus('خطأ في الاتصال: ' + error.message, 'error');
            }
        }
        
        // إرسال رسالة
        async function sendMessage() {
            updateConfig();
            
            const phoneNumber = document.getElementById('phone_number').value;
            const messageText = document.getElementById('message_text').value;
            
            if (!phoneNumber || !messageText) {
                showStatus('يرجى ملء رقم الهاتف والرسالة', 'error');
                return;
            }
            
            if (!apiUrl || !instanceName) {
                showStatus('يرجى تكوين إعدادات API أولاً', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${apiUrl}/message/sendText/${instanceName}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': apiKey
                    },
                    body: JSON.stringify({
                        number: phoneNumber,
                        text: messageText
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showStatus('تم إرسال الرسالة بنجاح!', 'success');
                    
                    // إضافة الرسالة للشات
                    addMessageToChat(messageText, 'sent', phoneNumber);
                    
                    // حفظ في قاعدة البيانات
                    await saveMessageToDB(phoneNumber, messageText, 'sent');
                    
                    // مسح النص
                    document.getElementById('message_text').value = '';
                } else {
                    const errorData = await response.json();
                    showStatus('فشل في إرسال الرسالة: ' + (errorData.message || 'خطأ غير معروف'), 'error');
                }
            } catch (error) {
                showStatus('خطأ في إرسال الرسالة: ' + error.message, 'error');
            }
        }
        
        // إضافة رسالة للشات
        function addMessageToChat(text, type, phone = '') {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
            
            messageDiv.innerHTML = `
                <div>${text}</div>
                <div class="message-time">${timeString} ${phone ? '- ' + phone : ''}</div>
            `;
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // حفظ الرسالة في قاعدة البيانات
        async function saveMessageToDB(phone, message, type) {
            try {
                const response = await fetch('save_message.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        phone: phone,
                        message: message,
                        type: type
                    })
                });
                
                if (!response.ok) {
                    console.error('فشل في حفظ الرسالة في قاعدة البيانات');
                }
            } catch (error) {
                console.error('خطأ في حفظ الرسالة:', error);
            }
        }
        
        // تحميل الرسائل من قاعدة البيانات
        async function loadMessages() {
            try {
                const response = await fetch('get_messages.php');
                if (response.ok) {
                    const messages = await response.json();
                    const chatContainer = document.getElementById('chatContainer');
                    chatContainer.innerHTML = '';
                    
                    messages.forEach(msg => {
                        addMessageToChat(msg.message, msg.type, msg.phone);
                    });
                    
                    showStatus('تم تحديث الرسائل', 'success');
                }
            } catch (error) {
                console.error('خطأ في تحميل الرسائل:', error);
            }
        }
        
        // تحميل إعدادات API المحفوظة
        async function loadSavedSettings() {
            try {
                const response = await fetch('api_settings.php');
                if (response.ok) {
                    const settings = await response.json();
                    document.getElementById('api_url').value = settings.api_url || 'http://localhost:8080';
                    document.getElementById('instance_name').value = settings.instance_name || 'default';
                    document.getElementById('api_key').value = settings.api_key || '';
                }
            } catch (error) {
                console.error('خطأ في تحميل الإعدادات:', error);
            }
        }

        // حفظ إعدادات API
        async function saveSettings() {
            updateConfig();

            try {
                const response = await fetch('api_settings.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        api_url: apiUrl,
                        instance_name: instanceName,
                        api_key: apiKey
                    })
                });

                if (response.ok) {
                    showStatus('تم حفظ الإعدادات بنجاح', 'success');
                } else {
                    showStatus('فشل في حفظ الإعدادات', 'error');
                }
            } catch (error) {
                showStatus('خطأ في حفظ الإعدادات: ' + error.message, 'error');
            }
        }

        // مسح الشات
        function clearChat() {
            if (confirm('هل أنت متأكد من مسح جميع الرسائل؟')) {
                const chatContainer = document.getElementById('chatContainer');
                chatContainer.innerHTML = '<div class="message received"><div>تم مسح الشات</div><div class="message-time">الآن</div></div>';
            }
        }

        // تحميل الرسائل عند بدء التشغيل
        window.onload = function() {
            loadSavedSettings();
            loadMessages();

            // تحديث الرسائل كل 30 ثانية
            setInterval(loadMessages, 30000);
        };

        // إرسال بالضغط على Enter
        document.getElementById('message_text').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    </script>
</body>
</html>
