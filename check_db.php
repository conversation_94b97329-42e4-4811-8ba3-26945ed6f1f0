<?php
require_once 'database.php';

echo "<h2>فحص قاعدة البيانات</h2>";

try {
    $pdo = getDBConnection();
    
    // عدد الرسائل
    $stmt = $pdo->query('SELECT COUNT(*) as total FROM messages');
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    echo "<p><strong>إجمالي الرسائل:</strong> $total</p>";
    
    // الرسائل المستقبلة
    $stmt = $pdo->query('SELECT COUNT(*) as received FROM messages WHERE type = "received"');
    $received = $stmt->fetch(PDO::FETCH_ASSOC)['received'];
    echo "<p><strong>الرسائل المستقبلة:</strong> $received</p>";
    
    // الرسائل المرسلة
    $stmt = $pdo->query('SELECT COUNT(*) as sent FROM messages WHERE type = "sent"');
    $sent = $stmt->fetch(PDO::FETCH_ASSOC)['sent'];
    echo "<p><strong>الرسائل المرسلة:</strong> $sent</p>";
    
    // آخر 10 رسائل
    echo "<h3>آخر 10 رسائل:</h3>";
    $stmt = $pdo->query('SELECT phone, message, type, created_at FROM messages ORDER BY created_at DESC LIMIT 10');
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($messages) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>التاريخ</th><th>النوع</th><th>الهاتف</th><th>الرسالة</th></tr>";
        foreach ($messages as $msg) {
            $typeColor = $msg['type'] === 'sent' ? 'blue' : 'green';
            echo "<tr>";
            echo "<td>" . htmlspecialchars($msg['created_at']) . "</td>";
            echo "<td style='color: $typeColor;'>" . htmlspecialchars($msg['type']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['phone']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['message']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ لا توجد رسائل في قاعدة البيانات</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    direction: rtl;
}
table {
    margin-top: 10px;
}
th, td {
    padding: 8px;
    text-align: right;
}
th {
    background: #e9ecef;
}
</style>
