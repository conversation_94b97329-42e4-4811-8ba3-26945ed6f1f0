<?php
// ملف لإرسال رسالة تجريبية

// إعدادات Evolution API
$apiUrl = 'http://localhost:8080';
$instanceName = 'whatsapp'; // حسب ما رأيت في السجل
$apiKey = '38CB5D996E4B-4526-B2E1-E113102B24AD'; // حسب ما رأيت في السجل

// رقم الهاتف المطلوب
$phoneNumber = '96892625160';
$message = 'مرحباً! هذه رسالة تجريبية من نظام WhatsApp Chat 🚀';

echo "<h2>إرسال رسالة تجريبية</h2>";
echo "<p><strong>الرقم:</strong> $phoneNumber</p>";
echo "<p><strong>الرسالة:</strong> $message</p>";

// إرسال الرسالة
$url = "$apiUrl/message/sendText/$instanceName";

$data = [
    'number' => $phoneNumber,
    'text' => $message
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'apikey: ' . $apiKey
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<h3>نتيجة الإرسال:</h3>";
echo "<p><strong>HTTP Code:</strong> $httpCode</p>";

if ($error) {
    echo "<p style='color: red;'><strong>خطأ:</strong> $error</p>";
} else {
    echo "<p style='color: green;'><strong>الاستجابة:</strong></p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    // حفظ الرسالة في قاعدة البيانات
    try {
        require_once 'database.php';
        $pdo = getDBConnection();
        
        $stmt = $pdo->prepare("
            INSERT INTO messages (phone, message, type) 
            VALUES (:phone, :message, 'sent')
        ");
        
        $stmt->bindParam(':phone', $phoneNumber);
        $stmt->bindParam(':message', $message);
        
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ تم حفظ الرسالة في قاعدة البيانات</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في حفظ الرسالة في قاعدة البيانات</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    }
}

// عرض آخر الرسائل من قاعدة البيانات
echo "<h3>آخر الرسائل في قاعدة البيانات:</h3>";
try {
    require_once 'database.php';
    $pdo = getDBConnection();
    
    $stmt = $pdo->query("
        SELECT phone, message, type, created_at 
        FROM messages 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($messages) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>الهاتف</th><th>الرسالة</th><th>النوع</th><th>التاريخ</th></tr>";
        foreach ($messages as $msg) {
            $typeColor = $msg['type'] === 'sent' ? 'blue' : 'green';
            echo "<tr>";
            echo "<td>" . htmlspecialchars($msg['phone']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['message']) . "</td>";
            echo "<td style='color: $typeColor;'>" . htmlspecialchars($msg['type']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد رسائل في قاعدة البيانات</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في قراءة قاعدة البيانات: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    direction: rtl;
}
table {
    margin-top: 10px;
}
th, td {
    padding: 8px;
    text-align: right;
}
th {
    background: #e9ecef;
}
pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}
</style>
