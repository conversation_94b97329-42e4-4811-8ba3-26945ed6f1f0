<?php
// اختبار الـ webhook بالبيانات الحقيقية من السجل

// بيانات حقيقية من السجل
$realWebhookData = [
    "event" => "messages.upsert",
    "instance" => "whatsapp",
    "data" => [
        "key" => [
            "remoteJid" => "<EMAIL>",
            "fromMe" => false,
            "id" => "TEST_MESSAGE_" . time()
        ],
        "pushName" => "الشبل",
        "status" => "DELIVERY_ACK",
        "message" => [
            "conversation" => "رسالة اختبار من الكود المحدث!"
        ],
        "messageType" => "conversation",
        "messageTimestamp" => time(),
        "instanceId" => "98855d3a-6a9a-4835-9f50-fc43641a904f",
        "source" => "android"
    ]
];

echo "<h2>اختبار Webhook بالبيانات الحقيقية</h2>";
echo "<h3>البيانات المرسلة:</h3>";
echo "<pre>" . json_encode($realWebhookData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

// إرسال البيانات إلى webhook
$webhookUrl = 'http://localhost/chatbot1/webhook.php';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $webhookUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($realWebhookData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($realWebhookData))
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<h3>نتيجة الـ Webhook:</h3>";
echo "<p><strong>HTTP Code:</strong> $httpCode</p>";

if ($error) {
    echo "<p style='color: red;'><strong>خطأ:</strong> $error</p>";
} else {
    echo "<p style='color: green;'><strong>الاستجابة:</strong></p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
}

// فحص قاعدة البيانات
echo "<h3>فحص قاعدة البيانات بعد الاختبار:</h3>";
try {
    require_once 'database.php';
    $pdo = getDBConnection();
    
    // البحث عن الرسالة الجديدة
    $stmt = $pdo->prepare("
        SELECT phone, message, type, created_at 
        FROM messages 
        WHERE message LIKE '%اختبار%' 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $testMessages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($testMessages) {
        echo "<p style='color: green;'>✅ تم العثور على رسائل اختبار:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>التاريخ</th><th>النوع</th><th>الهاتف</th><th>الرسالة</th></tr>";
        foreach ($testMessages as $msg) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($msg['created_at']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['type']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['phone']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['message']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ لم يتم العثور على رسائل اختبار</p>";
    }
    
    // آخر 5 رسائل عموماً
    echo "<h4>آخر 5 رسائل في قاعدة البيانات:</h4>";
    $stmt = $pdo->query("
        SELECT phone, message, type, created_at 
        FROM messages 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $allMessages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($allMessages) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>التاريخ</th><th>النوع</th><th>الهاتف</th><th>الرسالة</th></tr>";
        foreach ($allMessages as $msg) {
            $typeColor = $msg['type'] === 'sent' ? 'blue' : 'green';
            echo "<tr>";
            echo "<td>" . htmlspecialchars($msg['created_at']) . "</td>";
            echo "<td style='color: $typeColor;'>" . htmlspecialchars($msg['type']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['phone']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['message']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ لا توجد رسائل في قاعدة البيانات</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    direction: rtl;
}
pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}
table {
    margin-top: 10px;
}
th, td {
    padding: 8px;
    text-align: right;
}
th {
    background: #e9ecef;
}
</style>
